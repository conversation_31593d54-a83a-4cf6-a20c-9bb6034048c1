import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/utils/date_time_extension.dart';
import 'package:serwis_app/core/utils/date_time_utils.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/home/<USER>/home_cubit.dart'; // Ensure this import
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/repository/task_repository.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';
import 'package:serwis_app/visit/screens/visit_screen.dart';

class VisitsList extends StatelessWidget {
  const VisitsList({
    super.key,
    required this.visits,
    required this.isRefreshing,
  });

  final List<Visit> visits;
  final bool isRefreshing;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 12.0, right: 4),
            child: Row(
              children: [
                Text(
                  'Zaplanowane wizyty: ${visits.length}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                LoadingWidget(
                  isLoading: isRefreshing,
                  child: TextButton.icon(
                    onPressed: () {
                      context.read<HomeCubit>().refresh();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Odśwież'),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: visits.isEmpty
                ? const Center(
                    child: Text('Brak zaplanowanych wizyt'),
                  )
                : ListView(
                    children: DateTimeUtils.groupByDate(
                      visits,
                      (visit) => visit?.plannedDate,
                      (a, b) {
                        if (a.plannedDate == null) return -1;
                        if (b.plannedDate == null) return 1;

                        return a.plannedDate!.compareTo(b.plannedDate!);
                      },
                    ).entries.map(
                      (entry) {
                        final date = entry.key;
                        final visits = entry.value;
                        final startedVisitColor = Theme.of(context).colorScheme.onErrorContainer;
                        return Column(
                          children: [
                            ListTile(
                              title: Text(
                                date.format(),
                                style: Theme.of(context).textTheme.labelLarge,
                              ),
                              dense: true,
                              visualDensity: VisualDensity.compact,
                              trailing: Text('Ilość wizyt: ${visits.length}'),
                            ),
                            Card(
                              child: Column(
                                children: visits.map(
                                  (visit) {
                                    final isStarted = visit.status == VisitModelStatus.attended;

                                    return Container(
                                      margin: const EdgeInsets.symmetric(vertical: 2),
                                      decoration: isStarted
                                          ? BoxDecoration(
                                              color: startedVisitColor.withAlpha(20),
                                              // border: Border.all(
                                              //   color: Colors.green.withAlpha(80),
                                              //   width: 1.5,
                                              // ),
                                              // borderRadius: BorderRadius.circular(12),
                                            )
                                          : null,
                                      child: ListTile(
                                        title: Text(
                                          visit.location?.name ?? '',
                                          overflow: TextOverflow.ellipsis,
                                          style: isStarted
                                              ? TextStyle(
                                                  color: startedVisitColor,
                                                  fontWeight: FontWeight.w600,
                                                )
                                              : Theme.of(context).textTheme.bodyLarge,
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '${visit.location?.city}, ${visit.location?.address}',
                                              overflow: TextOverflow.ellipsis,
                                              style: isStarted
                                                  ? TextStyle(
                                                      color: startedVisitColor,
                                                    )
                                                  : null,
                                            ),
                                            if (isStarted)
                                              Padding(
                                                padding: const EdgeInsets.only(top: 6),
                                                child: Row(
                                                  children: [
                                                    Icon(
                                                      Icons.warning,
                                                      size: 16,
                                                      color: startedVisitColor,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'Rozpoczęto',
                                                      style: TextStyle(
                                                        color: startedVisitColor,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                          ],
                                        ),
                                        leading: SizedBox(
                                          width: 60,
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                '${visit.incidentId}/${visit.visitNumber}',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                  color: isStarted
                                                      ? startedVisitColor
                                                      : Theme.of(context).textTheme.bodyMedium?.color,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                              if (visit.visitPhotos != null && visit.visitPhotos!.isNotEmpty)
                                                Padding(
                                                  padding: const EdgeInsets.only(top: 4),
                                                  child: Icon(
                                                    Icons.photo_camera,
                                                    color: Colors.green.shade600,
                                                    size: 16,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        trailing: Icon(
                                          Icons.chevron_right,
                                          color: isStarted
                                              ? startedVisitColor
                                              : Theme.of(context).iconTheme.color?.withAlpha(150),
                                        ),
                                        contentPadding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 8,
                                        ),
                                        onTap: () async {
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (_) => BlocProvider(
                                                create: (_) => VisitCubit(
                                                  initialState: VisitState(
                                                    status: VisitStatus.ready,
                                                    visit: visit,
                                                  ),
                                                  visitRepository: context.read<VisitRepository>(),
                                                  taskRepository: context.read<TaskRepository>(),
                                                  homeCubit: context.read<HomeCubit>(),
                                                )..updateVisit(initial: true),
                                                child: const VisitScreen(),
                                              ),
                                            ),
                                          );
                                          if (result == true) {
                                            if (context.mounted) {
                                              context.read<HomeCubit>().refresh();
                                            }
                                          }
                                        },
                                      ),
                                    );
                                  },
                                ).toList(),
                              ),
                            ),
                          ],
                        );
                      },
                    ).toList(),
                  ),
          ),
        ],
      ),
    );
  }
}
