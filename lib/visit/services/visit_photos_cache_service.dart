import 'dart:developer';

import 'package:serwis_app/core/models/visit/visit_photo.dart';
import 'package:serwis_app/core/services/secure_storage.dart';
import 'package:serwis_app/visit/models/visit_photos_cache.dart';

/// Serwis do cache'owania zdjęć wizyt
///
/// Używa SecureStorage do przechowywania zdjęć z metadata.
/// Implementuje wzorzec Singleton dla globalnego dostępu.
class VisitPhotosCacheService {
  // Prywatna statyczna instancja klasy
  static final VisitPhotosCacheService _instance = VisitPhotosCacheService._internal();

  // Prywatny konstruktor
  VisitPhotosCacheService._internal();

  // Metoda fabryczna zwracająca instancję
  static VisitPhotosCacheService get instance => _instance;

  final SecureStorage _storage = SecureStorage.instance;

  // Prefiks dla kluczy cache'u
  static const String _cacheKeyPrefix = 'visit_photos_cache_';

  /// Generuje klucz cache'u dla danej wizyty
  String _getCacheKey(int visitId) => '$_cacheKeyPrefix$visitId';

  /// Pobiera cache'owane zdjęcia dla danej wizyty
  ///
  /// Zwraca null jeśli:
  /// - Brak cache'u dla danej wizyty
  /// - Cache jest nieważny (przekroczony TTL)
  /// - Wystąpił błąd podczas deserializacji
  Future<List<VisitPhoto>?> getCachedPhotos(int visitId, {Duration? ttl}) async {
    try {
      final cacheKey = _getCacheKey(visitId);
      final cachedData = await _storage.read(cacheKey);

      if (cachedData == null) {
        log('Cache miss for visit $visitId - no data found');
        return null;
      }

      final cache = VisitPhotosCache.fromJson(cachedData);

      // Sprawdź czy cache jest jeszcze ważny
      if (!cache.isValid(ttl: ttl ?? const Duration(days: 7))) {
        log('Cache expired for visit $visitId - removing');
        await clearVisitCache(visitId);
        return null;
      }

      log('Cache hit for visit $visitId - ${cache.photos.length} photos found');
      return cache.photos;
    } catch (e) {
      log('Error reading cache for visit $visitId: $e');
      // W przypadku błędu usuń uszkodzony cache
      await clearVisitCache(visitId);
      return null;
    }
  }

  /// Zapisuje zdjęcia do cache'u dla danej wizyty
  ///
  /// [visitId] - ID wizyty
  /// [photos] - lista zdjęć do cache'owania
  Future<void> setCachedPhotos(int visitId, List<VisitPhoto> photos) async {
    try {
      final cache = VisitPhotosCache(
        photos: photos,
        cachedAt: DateTime.now(),
        visitId: visitId,
      );

      final cacheKey = _getCacheKey(visitId);
      await _storage.write(cacheKey, cache.toJson());

      log('Cached ${photos.length} photos for visit $visitId');
    } catch (e) {
      log('Error caching photos for visit $visitId: $e');
      // Nie rzucamy błędu - cache'owanie jest opcjonalne
    }
  }

  /// Usuwa cache dla konkretnej wizyty
  Future<void> clearVisitCache(int visitId) async {
    try {
      final cacheKey = _getCacheKey(visitId);
      await _storage.write(cacheKey, null);
      log('Cleared cache for visit $visitId');
    } catch (e) {
      log('Error clearing cache for visit $visitId: $e');
    }
  }

  /// Usuwa cały cache zdjęć (wszystkie wizyty)
  ///
  /// Uwaga: Ta metoda wymaga iteracji przez wszystkie klucze w SecureStorage,
  /// co może być kosztowne. Używaj ostrożnie.
  Future<void> clearAllCache() async {
    try {
      // SecureStorage nie ma metody do listowania kluczy,
      // więc ta implementacja jest ograniczona.
      // W przyszłości można rozważyć przechowywanie listy visitId w osobnym kluczu.
      log('clearAllCache called - limited implementation');

      // Dla teraz możemy tylko logować - pełna implementacja wymagałaby
      // dodatkowego mechanizmu śledzenia aktywnych kluczy cache'u
    } catch (e) {
      log('Error clearing all cache: $e');
    }
  }

  /// Sprawdza czy istnieje cache dla danej wizyty
  Future<bool> hasCachedPhotos(int visitId) async {
    try {
      final cacheKey = _getCacheKey(visitId);
      final cachedData = await _storage.read(cacheKey);
      return cachedData != null;
    } catch (e) {
      log('Error checking cache existence for visit $visitId: $e');
      return false;
    }
  }

  /// Pobiera informacje o cache'u dla debugowania
  Future<Map<String, dynamic>?> getCacheInfo(int visitId) async {
    try {
      final cacheKey = _getCacheKey(visitId);
      final cachedData = await _storage.read(cacheKey);

      if (cachedData == null) {
        return null;
      }

      final cache = VisitPhotosCache.fromJson(cachedData);

      return {
        'visitId': cache.visitId,
        'photosCount': cache.photos.length,
        'cachedAt': cache.cachedAt.toIso8601String(),
        'isValid': cache.isValid(),
        'ageInHours': DateTime.now().difference(cache.cachedAt).inHours,
      };
    } catch (e) {
      log('Error getting cache info for visit $visitId: $e');
      return null;
    }
  }
}
