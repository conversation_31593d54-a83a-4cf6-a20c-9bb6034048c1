import 'dart:convert';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_photo.dart';
import 'package:serwis_app/core/services/photo_service.dart';
import 'package:serwis_app/visit/repository/visit_photos_repository.dart';

enum VisitPhotosStatus {
  loading,
  takingPhoto,
  uploadingPhoto,
  deletingPhoto,
  ready,
}

class VisitPhotosState {
  final VisitPhotosStatus status;
  final dynamic error;
  final List<File> photos;
  final List<String> failedPhotos;
  final Map<File, VisitPhoto> photoMapping; // Mapowanie File -> VisitPhoto dla ID

  const VisitPhotosState({
    required this.status,
    this.error,
    this.photos = const [],
    this.failedPhotos = const [],
    this.photoMapping = const {},
  });

  VisitPhotosState copyWith({
    VisitPhotosStatus? status,
    dynamic error,
    List<File>? photos,
    List<String>? failedPhotos,
    Map<File, VisitPhoto>? photoMapping,
  }) {
    return VisitPhotosState(
      status: status ?? this.status,
      error: error ?? this.error,
      photos: photos ?? this.photos,
      failedPhotos: failedPhotos ?? this.failedPhotos,
      photoMapping: photoMapping ?? this.photoMapping,
    );
  }
}

class VisitPhotosCubit extends Cubit<VisitPhotosState> {
  final PhotoService photoService;
  final VisitPhotosRepository visitPhotosRepository;
  final Visit visit;

  VisitPhotosCubit({
    required this.photoService,
    required this.visitPhotosRepository,
    required this.visit,
  }) : super(
          VisitPhotosState(
            status: VisitPhotosStatus.loading,
          ),
        );

  Future<void> loadPhotos() async {
    try {
      if (state.status != VisitPhotosStatus.uploadingPhoto) {
        emit(state.copyWith(status: VisitPhotosStatus.loading));
      }

      // Repository automatycznie sprawdzi cache i pobierze z API jeśli potrzeba
      final list = await visitPhotosRepository.getVisitPhotos(visit.id);
      final files = await _convertPhotosToFiles(list.data);

      emit(
        state.copyWith(
          status: VisitPhotosStatus.ready,
          photos: files.photos,
          failedPhotos: files.failedPhotos,
          photoMapping: files.photoMapping,
        ),
      );
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }

  Future<void> takePhoto() async {
    try {
      emit(state.copyWith(status: VisitPhotosStatus.takingPhoto));

      final photo = await photoService.takePhoto();

      emit(state.copyWith(status: VisitPhotosStatus.uploadingPhoto));

      if (photo != null) {
        await visitPhotosRepository.addVisitPhoto(visit.id, photo);
        // Repository automatycznie wyczyści cache po dodaniu zdjęcia
        // Przeładuj zdjęcia po dodaniu nowego
        await loadPhotos();
      } else {
        emit(state.copyWith(status: VisitPhotosStatus.ready));
      }
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }

  Future<void> deletePhoto(File photoFile) async {
    try {
      emit(state.copyWith(status: VisitPhotosStatus.deletingPhoto));

      // Znajdź VisitPhoto dla tego pliku
      final visitPhoto = state.photoMapping[photoFile];
      if (visitPhoto == null) {
        throw Exception('Nie można znaleźć ID zdjęcia do usunięcia');
      }

      // Usuń zdjęcie przez API
      await visitPhotosRepository.deleteVisitPhoto(visitPhoto.id, visit.id);

      // Przeładuj zdjęcia po usunięciu
      await loadPhotos();
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }

  /// Konwertuje zdjęcia (VisitPhoto) na pliki (File)
  ///
  /// Zwraca obiekt z listą plików, listą błędnych zdjęć i mapowaniem File -> VisitPhoto
  Future<({List<File> photos, List<String> failedPhotos, Map<File, VisitPhoto> photoMapping})> _convertPhotosToFiles(
    List<VisitPhoto> visitPhotos,
  ) async {
    final photos = <File>[];
    final failedPhotos = <String>[];
    final photoMapping = <File, VisitPhoto>{};
    final tempDir = await getTemporaryDirectory();

    for (final photoItem in visitPhotos) {
      try {
        final photoContent = photoItem.thumbnailContent;
        if (photoContent != null) {
          final fileName = photoItem.filename?.split('/').last ?? 'photo_${photoItem.id}.jpg';
          final photoData = base64Decode(photoContent);

          final photoPath = '${tempDir.path}/$fileName';
          final photoFile = await File(photoPath).create();
          await photoFile.writeAsBytes(photoData);

          photos.add(photoFile);
          photoMapping[photoFile] = photoItem; // Dodaj mapowanie
        } else {
          failedPhotos.add(photoItem.filename ?? 'Uszkodzone zdjęcie ${photoItem.id}');
        }
      } catch (e) {
        failedPhotos.add(photoItem.filename ?? 'Uszkodzone zdjęcie ${photoItem.id}');
      }
    }

    return (photos: photos, failedPhotos: failedPhotos, photoMapping: photoMapping);
  }
}
