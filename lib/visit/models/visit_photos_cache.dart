import 'dart:convert';

import 'package:serwis_app/core/models/visit/visit_photo.dart';

/// Wrapper dla cache'owan<PERSON> zd<PERSON> wizyty z metadata
class VisitPhotosCache {
  final List<VisitPhoto> photos;
  final DateTime cachedAt;
  final int visitId;

  const VisitPhotosCache({
    required this.photos,
    required this.cachedAt,
    required this.visitId,
  });

  /// Sprawdza czy cache jest jeszcze ważny (domyślnie 7 dni)
  bool isValid({Duration ttl = const Duration(days: 7)}) {
    return DateTime.now().difference(cachedAt) < ttl;
  }

  Map<String, dynamic> toMap() {
    return {
      'photos': photos.map((photo) => photo.toMap()).toList(),
      'cachedAt': cachedAt.millisecondsSinceEpoch,
      'visitId': visitId,
    };
  }

  factory VisitPhotosCache.fromMap(Map<String, dynamic> map) {
    return VisitPhotosCache(
      photos: List<VisitPhoto>.from(
        map['photos']?.map((x) => VisitPhoto.fromMap(x)) ?? [],
      ),
      cachedAt: DateTime.fromMillisecondsSinceEpoch(map['cachedAt'] ?? 0),
      visitId: map['visitId']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory VisitPhotosCache.fromJson(String source) => VisitPhotosCache.fromMap(json.decode(source));
}
