import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/widgets/error_container.dart';
import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';
import 'package:serwis_app/visit/widgets/delete_photo_confirmation_modal.dart';

class VisitPhotoListView extends StatelessWidget {
  const VisitPhotoListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<VisitPhotosCubit, VisitPhotosState, VisitPhotosState>(
      selector: (state) => state,
      builder: (context, state) {
        final photos = state.photos;
        final failedPhotos = state.failedPhotos;
        final totalItems = photos.length + failedPhotos.length;

        if (totalItems == 0) {
          return const Center(
            child: Text('Brak zdjęć'),
          );
        }

        return Column(
          children: [
            if (failedPhotos.isNotEmpty)
              ErrorContainer(
                error: '<PERSON>e udało się wczytać ${failedPhotos.length} zdj<PERSON><PERSON>',
                message: 'Niektóre pliki mogą być uszkodzone',
                visible: true,
              ),
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(8.0),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 8.0,
                  mainAxisSpacing: 8.0,
                  childAspectRatio: 1.0,
                ),
                itemCount: totalItems,
                itemBuilder: (context, index) {
                  if (index < photos.length) {
                    return _buildPhotoItem(context, photos[index]);
                  } else {
                    final failedIndex = index - photos.length;
                    return _buildFailedPhotoPlaceholder(failedPhotos[failedIndex]);
                  }
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPhotoItem(BuildContext context, File photo) {
    return BlocSelector<VisitPhotosCubit, VisitPhotosState, VisitPhotosStatus>(
      selector: (state) => state.status,
      builder: (context, status) {
        final isDeleting = status == VisitPhotosStatus.deletingPhoto;

        return GestureDetector(
          onLongPress: isDeleting ? null : () => _showDeleteConfirmation(context, photo),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Image.file(
                  photo,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildFailedPhotoPlaceholder(photo.path.split('/').last);
                  },
                ),
              ),
              if (isDeleting)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, File photo) {
    DeletePhotoConfirmationModal.show(
      context,
      photoFile: photo,
      onConfirm: () {
        context.read<VisitPhotosCubit>().deletePhoto(photo);
      },
    );
  }

  Widget _buildFailedPhotoPlaceholder(String filename) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image,
            size: 40,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'Plik uszkodzony',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              filename,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
